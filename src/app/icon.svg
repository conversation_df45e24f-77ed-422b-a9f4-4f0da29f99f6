<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6366f1" />
      <stop offset="50%" stop-color="#4f46e5" />
      <stop offset="100%" stop-color="#7c3aed" />
    </linearGradient>
    <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#f59e0b" />
      <stop offset="100%" stop-color="#d97706" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Main Cube Structure -->
  <g transform="translate(4, 4)">
    <!-- Back face -->
    <path
      d="M6 8 L18 8 L18 20 L6 20 Z"
      fill="url(#cubeGradient)"
      opacity="0.7"
    />
    
    <!-- Right face -->
    <path
      d="M18 8 L22 4 L22 16 L18 20 Z"
      fill="url(#cubeGradient)"
      opacity="0.8"
    />
    
    <!-- Top face -->
    <path
      d="M6 8 L10 4 L22 4 L18 8 Z"
      fill="url(#cubeGradient)"
      opacity="0.9"
    />

    <!-- Neural Network Nodes -->
    <g filter="url(#glow)">
      <!-- Central hub node -->
      <circle cx="12" cy="14" r="1.5" fill="url(#neuralGradient)" />
      
      <!-- Connected nodes -->
      <circle cx="8" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.8" />
      <circle cx="16" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.8" />
      <circle cx="9" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.8" />
      <circle cx="15" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.8" />
      
      <!-- Connection lines -->
      <line x1="12" y1="14" x2="8" y2="11" stroke="url(#neuralGradient)" stroke-width="0.5" opacity="0.6" />
      <line x1="12" y1="14" x2="16" y2="11" stroke="url(#neuralGradient)" stroke-width="0.5" opacity="0.6" />
      <line x1="12" y1="14" x2="9" y2="17" stroke="url(#neuralGradient)" stroke-width="0.5" opacity="0.6" />
      <line x1="12" y1="14" x2="15" y2="17" stroke="url(#neuralGradient)" stroke-width="0.5" opacity="0.6" />
      
      <!-- Cross connections -->
      <line x1="8" y1="11" x2="16" y2="11" stroke="url(#neuralGradient)" stroke-width="0.3" opacity="0.4" />
      <line x1="9" y1="17" x2="15" y2="17" stroke="url(#neuralGradient)" stroke-width="0.3" opacity="0.4" />
    </g>

    <!-- Subtle data points -->
    <g opacity="0.3">
      <rect x="7" y="9" width="1" height="1" fill="#ffffff" rx="0.5" />
      <rect x="17" y="12" width="1" height="1" fill="#ffffff" rx="0.5" />
      <rect x="10" y="18" width="1" height="1" fill="#ffffff" rx="0.5" />
    </g>
  </g>
</svg>
