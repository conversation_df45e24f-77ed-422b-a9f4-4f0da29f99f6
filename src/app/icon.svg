<svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#6366f1" />
      <stop offset="30%" stop-color="#5b21b6" />
      <stop offset="70%" stop-color="#4f46e5" />
      <stop offset="100%" stop-color="#7c3aed" />
    </linearGradient>
    <linearGradient id="topFaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#8b5cf6" />
      <stop offset="100%" stop-color="#6366f1" />
    </linearGradient>
    <linearGradient id="rightFaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" />
      <stop offset="100%" stop-color="#3730a3" />
    </linearGradient>
    <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#fbbf24" />
      <stop offset="50%" stop-color="#f59e0b" />
      <stop offset="100%" stop-color="#d97706" />
    </linearGradient>
  </defs>

  <!-- Main Cube Structure -->
  <g transform="translate(3, 3)">
    <!-- Back face -->
    <path
      d="M4 8 L18 8 L18 20 L4 20 Z"
      fill="url(#cubeGradient)"
      stroke="#312e81"
      stroke-width="0.3"
      opacity="0.95"
    />

    <!-- Right face -->
    <path
      d="M18 8 L22 5 L22 17 L18 20 Z"
      fill="url(#rightFaceGradient)"
      stroke="#312e81"
      stroke-width="0.3"
      opacity="0.9"
    />

    <!-- Top face -->
    <path
      d="M4 8 L8 5 L22 5 L18 8 Z"
      fill="url(#topFaceGradient)"
      stroke="#312e81"
      stroke-width="0.3"
      opacity="0.95"
    />

    <!-- Neural Network -->
    <g>
      <!-- Central hub -->
      <circle cx="11" cy="14" r="1.5" fill="url(#neuralGradient)" stroke="#ffffff" stroke-width="0.2" opacity="0.95" />

      <!-- Connected nodes -->
      <circle cx="8" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.85" />
      <circle cx="14" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.85" />
      <circle cx="7" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.85" />
      <circle cx="15" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.85" />

      <!-- Connections -->
      <line x1="11" y1="14" x2="8" y2="11" stroke="url(#neuralGradient)" stroke-width="0.6" opacity="0.7" stroke-linecap="round" />
      <line x1="11" y1="14" x2="14" y2="11" stroke="url(#neuralGradient)" stroke-width="0.6" opacity="0.7" stroke-linecap="round" />
      <line x1="11" y1="14" x2="7" y2="17" stroke="url(#neuralGradient)" stroke-width="0.6" opacity="0.7" stroke-linecap="round" />
      <line x1="11" y1="14" x2="15" y2="17" stroke="url(#neuralGradient)" stroke-width="0.6" opacity="0.7" stroke-linecap="round" />

      <!-- Cross connections -->
      <line x1="8" y1="11" x2="14" y2="11" stroke="url(#neuralGradient)" stroke-width="0.3" opacity="0.4" stroke-linecap="round" />
      <line x1="7" y1="17" x2="15" y2="17" stroke="url(#neuralGradient)" stroke-width="0.3" opacity="0.4" stroke-linecap="round" />
    </g>

    <!-- Data points -->
    <g opacity="0.4">
      <rect x="6" y="9" width="1" height="1" fill="#ffffff" rx="0.2" />
      <rect x="16" y="12" width="1" height="1" fill="#ffffff" rx="0.2" />
      <rect x="9" y="19" width="1" height="1" fill="#ffffff" rx="0.2" />
    </g>
  </g>
</svg>
