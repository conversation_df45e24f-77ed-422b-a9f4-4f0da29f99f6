'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { 
  <PERSON>rkles, 
  Zap, 
  Search, 
  BookOpen, 
  Code, 
  Palette, 
  BarChart3, 
  MessageSquare,
  ArrowRight,
  Lightbulb,
  Target,
  Rocket
} from 'lucide-react';

interface WelcomeScreenProps {
  onSuggestionClick?: (prompt: string) => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onSuggestionClick }) => {
  const suggestionCards = [
    {
      icon: Search,
      title: "Find AI Tools",
      description: "What's the best AI tool for content creation?",
      gradient: "from-blue-500 to-cyan-500",
      category: "Discovery"
    },
    {
      icon: Code,
      title: "Coding Assistant",
      description: "I need help with coding - what AI tools do you recommend?",
      gradient: "from-green-500 to-emerald-500",
      category: "Development"
    },
    {
      icon: BookOpen,
      title: "Learning Resources",
      description: "Show me beginner-friendly AI courses and tutorials",
      gradient: "from-purple-500 to-violet-500",
      category: "Education"
    },
    {
      icon: Palette,
      title: "Creative Tools",
      description: "I'm looking for AI image and video generators",
      gradient: "from-pink-500 to-rose-500",
      category: "Creative"
    },
    {
      icon: BarChart3,
      title: "Data Analysis",
      description: "Help me find tools for data analysis and visualization",
      gradient: "from-orange-500 to-amber-500",
      category: "Analytics"
    },
    {
      icon: MessageSquare,
      title: "Business Solutions",
      description: "What are the top AI tools for small businesses?",
      gradient: "from-indigo-500 to-purple-500",
      category: "Business"
    }
  ];

  const features = [
    {
      icon: Target,
      title: "Personalized Recommendations",
      description: "Get AI tool suggestions tailored to your specific needs"
    },
    {
      icon: Lightbulb,
      title: "Expert Insights",
      description: "Access detailed reviews and comparisons from AI experts"
    },
    {
      icon: Rocket,
      title: "Stay Updated",
      description: "Discover the latest AI tools and technologies as they emerge"
    }
  ];

  return (
    <div className="flex flex-col items-center justify-center h-full text-center py-8 px-4 max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="mb-12">
        <div className="w-20 h-20 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-xl">
          <Sparkles className="w-10 h-10 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Welcome to AIventory
        </h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
          I'm your intelligent AI assistant, ready to help you discover the perfect AI tools, 
          courses, and resources for your projects. Let's find exactly what you need!
        </p>
      </div>

      {/* Suggestion Cards */}
      <div className="w-full mb-12">
        <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center justify-center gap-2">
          <Zap className="w-5 h-5 text-indigo-600" />
          Try asking me about...
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {suggestionCards.map((card, index) => (
            <Card
              key={index}
              className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 bg-white/80 backdrop-blur-sm overflow-hidden focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2"
              onClick={() => onSuggestionClick?.(card.description)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onSuggestionClick?.(card.description);
                }
              }}
              tabIndex={0}
              role="button"
              aria-label={`Ask about ${card.title}: ${card.description}`}
            >
              <CardContent className="p-5">
                <div className="flex items-start gap-3 mb-3">
                  <div className={`w-10 h-10 bg-gradient-to-br ${card.gradient} rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200`}>
                    <card.icon className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 text-sm">{card.title}</h3>
                      <span className="text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                        {card.category}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 leading-relaxed">
                      "{card.description}"
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Features */}
      <div className="w-full">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">What I can help you with</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                <feature.icon className="w-6 h-6 text-indigo-600" />
              </div>
              <h4 className="font-medium text-gray-900 mb-2">{feature.title}</h4>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-12 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-2xl border border-indigo-100">
        <p className="text-gray-700 mb-4">
          <strong>Ready to get started?</strong> Type your question below or click on any suggestion above.
        </p>
        <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
          <MessageSquare className="w-4 h-4" />
          <span>I'm here to help you navigate the AI landscape</span>
        </div>
      </div>
    </div>
  );
};

export default WelcomeScreen;
