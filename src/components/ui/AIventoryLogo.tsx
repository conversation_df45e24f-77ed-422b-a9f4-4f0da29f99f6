import React from 'react';

interface AIventoryLogoProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
  variant?: 'default' | 'simplified' | 'minimal';
}

const AIventoryLogo: React.FC<AIventoryLogoProps> = ({
  size = 'md',
  className = '',
  showText = true,
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  // Simplified variant for small sizes or minimal contexts
  const SimplifiedLogo = () => (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full"
    >
      <defs>
        <linearGradient id="simpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#6366f1" />
          <stop offset="100%" stopColor="#7c3aed" />
        </linearGradient>
      </defs>
      <rect x="4" y="6" width="16" height="12" rx="2" fill="url(#simpleGradient)" opacity="0.9"/>
      <circle cx="12" cy="12" r="2" fill="#f59e0b" />
      <circle cx="8" cy="10" r="1" fill="#f59e0b" opacity="0.7" />
      <circle cx="16" cy="14" r="1" fill="#f59e0b" opacity="0.7" />
      <line x1="12" y1="12" x2="8" y2="10" stroke="#f59e0b" strokeWidth="0.5" opacity="0.5" />
      <line x1="12" y1="12" x2="16" y2="14" stroke="#f59e0b" strokeWidth="0.5" opacity="0.5" />
    </svg>
  );

  // Minimal variant - just the essential shape
  const MinimalLogo = () => (
    <svg
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full"
    >
      <defs>
        <linearGradient id="minimalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#6366f1" />
          <stop offset="100%" stopColor="#7c3aed" />
        </linearGradient>
      </defs>
      <rect x="6" y="6" width="12" height="12" rx="2" fill="url(#minimalGradient)"/>
      <circle cx="12" cy="12" r="2" fill="#f59e0b" />
    </svg>
  );

  const renderLogo = () => {
    if (variant === 'simplified') return <SimplifiedLogo />;
    if (variant === 'minimal') return <MinimalLogo />;

    // Default detailed logo
    return (
        <svg
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full"
        >
          {/* Gradient Definitions */}
          <defs>
            <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#6366f1" />
              <stop offset="50%" stopColor="#4f46e5" />
              <stop offset="100%" stopColor="#7c3aed" />
            </linearGradient>
            <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#f59e0b" />
              <stop offset="100%" stopColor="#d97706" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="1" result="coloredBlur"/>
              <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>

          {/* Main Cube Structure */}
          <g transform="translate(4, 4)">
            {/* Back face */}
            <path
              d="M6 8 L18 8 L18 20 L6 20 Z"
              fill="url(#cubeGradient)"
              opacity="0.7"
            />
            
            {/* Right face */}
            <path
              d="M18 8 L22 4 L22 16 L18 20 Z"
              fill="url(#cubeGradient)"
              opacity="0.8"
            />
            
            {/* Top face */}
            <path
              d="M6 8 L10 4 L22 4 L18 8 Z"
              fill="url(#cubeGradient)"
              opacity="0.9"
            />

            {/* Neural Network Nodes */}
            <g filter="url(#glow)">
              {/* Central hub node */}
              <circle cx="12" cy="14" r="1.5" fill="url(#neuralGradient)" />
              
              {/* Connected nodes */}
              <circle cx="8" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.8" />
              <circle cx="16" cy="11" r="1" fill="url(#neuralGradient)" opacity="0.8" />
              <circle cx="9" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.8" />
              <circle cx="15" cy="17" r="1" fill="url(#neuralGradient)" opacity="0.8" />
              
              {/* Connection lines */}
              <line x1="12" y1="14" x2="8" y2="11" stroke="url(#neuralGradient)" strokeWidth="0.5" opacity="0.6" />
              <line x1="12" y1="14" x2="16" y2="11" stroke="url(#neuralGradient)" strokeWidth="0.5" opacity="0.6" />
              <line x1="12" y1="14" x2="9" y2="17" stroke="url(#neuralGradient)" strokeWidth="0.5" opacity="0.6" />
              <line x1="12" y1="14" x2="15" y2="17" stroke="url(#neuralGradient)" strokeWidth="0.5" opacity="0.6" />
              
              {/* Cross connections */}
              <line x1="8" y1="11" x2="16" y2="11" stroke="url(#neuralGradient)" strokeWidth="0.3" opacity="0.4" />
              <line x1="9" y1="17" x2="15" y2="17" stroke="url(#neuralGradient)" strokeWidth="0.3" opacity="0.4" />
            </g>

            {/* Subtle data points */}
            <g opacity="0.3">
              <rect x="7" y="9" width="1" height="1" fill="#ffffff" rx="0.5" />
              <rect x="17" y="12" width="1" height="1" fill="#ffffff" rx="0.5" />
              <rect x="10" y="18" width="1" height="1" fill="#ffffff" rx="0.5" />
            </g>
          </g>
        </svg>
    );
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Logo Icon */}
      <div className={`${sizeClasses[size]} relative`}>
        {renderLogo()}
      </div>

      {/* Brand Text */}
      {showText && (
        <span className={`font-bold text-gray-900 ${textSizeClasses[size]}`}>
          AIventory
        </span>
      )}
    </div>
  );
};

export default AIventoryLogo;
