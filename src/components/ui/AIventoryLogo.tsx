import React from 'react';

interface AIventoryLogoProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showText?: boolean;
  variant?: 'default' | 'simplified' | 'minimal';
}

const AIventoryLogo: React.FC<AIventoryLogoProps> = ({
  size = 'md',
  className = '',
  showText = true,
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8', 
    lg: 'w-12 h-12'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  // Simplified variant for small sizes or minimal contexts
  const SimplifiedLogo = () => (
    <svg
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full drop-shadow-md"
    >
      <defs>
        <linearGradient id="simpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#6366f1" />
          <stop offset="50%" stopColor="#4f46e5" />
          <stop offset="100%" stopColor="#7c3aed" />
        </linearGradient>
        <linearGradient id="simpleNeuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#fbbf24" />
          <stop offset="100%" stopColor="#f59e0b" />
        </linearGradient>
      </defs>

      {/* Simplified cube */}
      <g transform="translate(4, 4)">
        <path d="M2 8 L14 8 L14 18 L2 18 Z" fill="url(#simpleGradient)" opacity="0.9"/>
        <path d="M14 8 L18 5 L18 15 L14 18 Z" fill="url(#simpleGradient)" opacity="0.7"/>
        <path d="M2 8 L6 5 L18 5 L14 8 Z" fill="url(#simpleGradient)" opacity="0.95"/>

        {/* Simplified neural network */}
        <circle cx="8" cy="13" r="1.5" fill="url(#simpleNeuralGradient)" />
        <circle cx="5" cy="11" r="0.8" fill="url(#simpleNeuralGradient)" opacity="0.8" />
        <circle cx="11" cy="11" r="0.8" fill="url(#simpleNeuralGradient)" opacity="0.8" />
        <circle cx="6" cy="15" r="0.8" fill="url(#simpleNeuralGradient)" opacity="0.8" />
        <circle cx="10" cy="15" r="0.8" fill="url(#simpleNeuralGradient)" opacity="0.8" />

        <line x1="8" y1="13" x2="5" y2="11" stroke="url(#simpleNeuralGradient)" strokeWidth="0.5" opacity="0.6" />
        <line x1="8" y1="13" x2="11" y2="11" stroke="url(#simpleNeuralGradient)" strokeWidth="0.5" opacity="0.6" />
        <line x1="8" y1="13" x2="6" y2="15" stroke="url(#simpleNeuralGradient)" strokeWidth="0.5" opacity="0.6" />
        <line x1="8" y1="13" x2="10" y2="15" stroke="url(#simpleNeuralGradient)" strokeWidth="0.5" opacity="0.6" />
      </g>
    </svg>
  );

  // Minimal variant - just the essential shape
  const MinimalLogo = () => (
    <svg
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="w-full h-full drop-shadow-sm"
    >
      <defs>
        <linearGradient id="minimalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#6366f1" />
          <stop offset="50%" stopColor="#4f46e5" />
          <stop offset="100%" stopColor="#7c3aed" />
        </linearGradient>
        <linearGradient id="minimalNeuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#fbbf24" />
          <stop offset="100%" stopColor="#f59e0b" />
        </linearGradient>
      </defs>

      {/* Minimal cube */}
      <g transform="translate(2, 2)">
        <path d="M4 8 L16 8 L16 20 L4 20 Z" fill="url(#minimalGradient)" opacity="0.9"/>
        <path d="M16 8 L20 4 L20 16 L16 20 Z" fill="url(#minimalGradient)" opacity="0.7"/>
        <path d="M4 8 L8 4 L20 4 L16 8 Z" fill="url(#minimalGradient)" opacity="0.95"/>

        {/* Single central node */}
        <circle cx="10" cy="14" r="2" fill="url(#minimalNeuralGradient)" opacity="0.9" />
        <circle cx="7" cy="11" r="1" fill="url(#minimalNeuralGradient)" opacity="0.6" />
        <circle cx="13" cy="17" r="1" fill="url(#minimalNeuralGradient)" opacity="0.6" />
      </g>
    </svg>
  );

  const renderLogo = () => {
    if (variant === 'simplified') return <SimplifiedLogo />;
    if (variant === 'minimal') return <MinimalLogo />;

    // Default detailed logo
    return (
        <svg
          viewBox="0 0 40 40"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="w-full h-full drop-shadow-lg"
        >
          {/* Enhanced Gradient Definitions */}
          <defs>
            <linearGradient id="cubeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#6366f1" />
              <stop offset="30%" stopColor="#5b21b6" />
              <stop offset="70%" stopColor="#4f46e5" />
              <stop offset="100%" stopColor="#7c3aed" />
            </linearGradient>
            <linearGradient id="topFaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#8b5cf6" />
              <stop offset="100%" stopColor="#6366f1" />
            </linearGradient>
            <linearGradient id="rightFaceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#4f46e5" />
              <stop offset="100%" stopColor="#3730a3" />
            </linearGradient>
            <linearGradient id="neuralGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#fbbf24" />
              <stop offset="50%" stopColor="#f59e0b" />
              <stop offset="100%" stopColor="#d97706" />
            </linearGradient>
            <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
              <feGaussianBlur stdDeviation="1.5" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
            <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
              <feDropShadow dx="1" dy="2" stdDeviation="1" floodColor="#000000" floodOpacity="0.2"/>
            </filter>
          </defs>

          {/* Main Cube Structure - Enhanced */}
          <g transform="translate(6, 6)" filter="url(#shadow)">
            {/* Back face - Main front */}
            <path
              d="M4 10 L20 10 L20 24 L4 24 Z"
              fill="url(#cubeGradient)"
              stroke="#312e81"
              strokeWidth="0.5"
              opacity="0.95"
            />

            {/* Right face - Side panel */}
            <path
              d="M20 10 L26 6 L26 20 L20 24 Z"
              fill="url(#rightFaceGradient)"
              stroke="#312e81"
              strokeWidth="0.5"
              opacity="0.9"
            />

            {/* Top face - Top panel */}
            <path
              d="M4 10 L10 6 L26 6 L20 10 Z"
              fill="url(#topFaceGradient)"
              stroke="#312e81"
              strokeWidth="0.5"
              opacity="0.95"
            />

            {/* Enhanced Neural Network */}
            <g filter="url(#glow)">
              {/* Central processing hub */}
              <circle cx="12" cy="17" r="2" fill="url(#neuralGradient)" stroke="#ffffff" strokeWidth="0.3" opacity="0.95" />

              {/* Primary nodes */}
              <circle cx="8" cy="13" r="1.2" fill="url(#neuralGradient)" opacity="0.85" />
              <circle cx="16" cy="13" r="1.2" fill="url(#neuralGradient)" opacity="0.85" />
              <circle cx="7" cy="21" r="1.2" fill="url(#neuralGradient)" opacity="0.85" />
              <circle cx="17" cy="21" r="1.2" fill="url(#neuralGradient)" opacity="0.85" />

              {/* Secondary nodes */}
              <circle cx="12" cy="12" r="0.8" fill="url(#neuralGradient)" opacity="0.7" />
              <circle cx="12" cy="22" r="0.8" fill="url(#neuralGradient)" opacity="0.7" />

              {/* Primary connections */}
              <line x1="12" y1="17" x2="8" y2="13" stroke="url(#neuralGradient)" strokeWidth="0.8" opacity="0.7" strokeLinecap="round" />
              <line x1="12" y1="17" x2="16" y2="13" stroke="url(#neuralGradient)" strokeWidth="0.8" opacity="0.7" strokeLinecap="round" />
              <line x1="12" y1="17" x2="7" y2="21" stroke="url(#neuralGradient)" strokeWidth="0.8" opacity="0.7" strokeLinecap="round" />
              <line x1="12" y1="17" x2="17" y2="21" stroke="url(#neuralGradient)" strokeWidth="0.8" opacity="0.7" strokeLinecap="round" />

              {/* Secondary connections */}
              <line x1="12" y1="17" x2="12" y2="12" stroke="url(#neuralGradient)" strokeWidth="0.6" opacity="0.5" strokeLinecap="round" />
              <line x1="12" y1="17" x2="12" y2="22" stroke="url(#neuralGradient)" strokeWidth="0.6" opacity="0.5" strokeLinecap="round" />

              {/* Cross connections */}
              <line x1="8" y1="13" x2="16" y2="13" stroke="url(#neuralGradient)" strokeWidth="0.4" opacity="0.4" strokeLinecap="round" />
              <line x1="7" y1="21" x2="17" y2="21" stroke="url(#neuralGradient)" strokeWidth="0.4" opacity="0.4" strokeLinecap="round" />
            </g>

            {/* Data visualization elements */}
            <g opacity="0.4">
              <rect x="6" y="11" width="1.5" height="1.5" fill="#ffffff" rx="0.3" />
              <rect x="18" y="14" width="1.5" height="1.5" fill="#ffffff" rx="0.3" />
              <rect x="9" y="23" width="1.5" height="1.5" fill="#ffffff" rx="0.3" />
              <circle cx="15" cy="11" r="0.5" fill="#ffffff" opacity="0.6" />
              <circle cx="9" cy="15" r="0.5" fill="#ffffff" opacity="0.6" />
            </g>
          </g>
        </svg>
    );
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Logo Icon */}
      <div className={`${sizeClasses[size]} relative`}>
        {renderLogo()}
      </div>

      {/* Brand Text */}
      {showText && (
        <span className={`font-bold text-gray-900 ${textSizeClasses[size]}`}>
          AIventory
        </span>
      )}
    </div>
  );
};

export default AIventoryLogo;
